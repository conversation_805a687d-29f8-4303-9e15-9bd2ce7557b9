package com.lc.billion.monitoring.config;

import com.lc.billion.monitoring.aop.CountedAspect;
import com.lc.billion.monitoring.aop.TimedAspect;
import com.lc.billion.monitoring.exporter.PrometheusExporter;
import com.lc.billion.monitoring.metrics.JvmMetricsCollector;
import com.lc.billion.monitoring.metrics.WebSocketMetricsCollector;
import com.lc.billion.monitoring.registry.MeterRegistryManager;
import io.micrometer.prometheusmetrics.PrometheusConfig;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.env.Environment;

import java.util.Optional;

/**
 * 监控模块配置类
 * <p>
 * 自动配置所有监控组件
 * </p>
 *
 */
@Configuration
@EnableAspectJAutoProxy
public class MonitoringConfiguration {

    /**
     * Prometheus注册表
     */
    @Bean
    public PrometheusMeterRegistry prometheusMeterRegistry() {
        return new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
    }

    /**
     * 注册表管理器
     */
    @Bean
    public MeterRegistryManager meterRegistryManager(PrometheusMeterRegistry prometheusMeterRegistry,
                                                     Environment environment) {
        String applicationName = Optional.ofNullable(environment.getProperty("monitoring.application.name"))
                .orElse(environment.getProperty("spring.application.name", "unknown"));
        
        return new MeterRegistryManager(prometheusMeterRegistry, applicationName);
    }

    /**
     * JVM指标收集器
     */
    @Bean
    public JvmMetricsCollector jvmMetricsCollector(MeterRegistryManager meterRegistryManager,
                                                   Environment environment) {
        boolean enableJvm = Boolean.parseBoolean(
                environment.getProperty("monitoring.jvm.enabled", "true"));
        
        return new JvmMetricsCollector(meterRegistryManager, enableJvm);
    }

    /**
     * WebSocket指标收集器
     */
    @Bean
    public WebSocketMetricsCollector webSocketMetricsCollector(MeterRegistryManager meterRegistryManager,
                                                               Environment environment) {
        boolean enableWebSocket = Boolean.parseBoolean(
                environment.getProperty("monitoring.websocket.enabled", "true"));
        
        return new WebSocketMetricsCollector(meterRegistryManager, enableWebSocket);
    }

    /**
     * 计时切面
     */
    @Bean
    public TimedAspect timedAspect(MeterRegistryManager meterRegistryManager) {
        return new TimedAspect(meterRegistryManager);
    }

    /**
     * 计数切面
     */
    @Bean
    public CountedAspect countedAspect(MeterRegistryManager meterRegistryManager) {
        return new CountedAspect(meterRegistryManager);
    }

    /**
     * Prometheus导出器
     */
    @Bean
    public PrometheusExporter prometheusExporter(PrometheusMeterRegistry prometheusMeterRegistry,
                                                 Environment environment) {
        int port = Integer.parseInt(environment.getProperty("monitoring.prometheus.port", "9090"));
        String path = environment.getProperty("monitoring.prometheus.path", "/metrics");
        
        return new PrometheusExporter(prometheusMeterRegistry, port, path);
    }
} 