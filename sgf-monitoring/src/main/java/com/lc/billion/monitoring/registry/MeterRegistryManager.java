package com.lc.billion.monitoring.registry;

import io.micrometer.core.instrument.*;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * 指标注册表管理器
 * <p>
 * 提供统一的指标创建和管理接口，支持异步处理以减少性能影响
 * </p>
 */
public class MeterRegistryManager {
    
    private static final Logger log = LoggerFactory.getLogger(MeterRegistryManager.class);
    
    private final PrometheusMeterRegistry meterRegistry;
    private final String applicationName;
    
    public MeterRegistryManager(PrometheusMeterRegistry meterRegistry, String applicationName) {
        this.meterRegistry = meterRegistry;
        this.applicationName = applicationName;
        
        // 添加通用标签
        meterRegistry.config().commonTags("application", applicationName);
        
        log.info("监控注册表管理器初始化完成，应用名: {}", applicationName);
    }
    
    /**
     * 获取原始注册表
     */
    public PrometheusMeterRegistry getRegistry() {
        return meterRegistry;
    }
    
    /**
     * 创建计时器
     */
    public Timer timer(String name, String description, String... tags) {
        return Timer.builder(name)
                .description(description)
                .tags(tags)
                .publishPercentileHistogram(true)
                .register(meterRegistry);
    }
    
    /**
     * 创建计数器
     */
    public Counter counter(String name, String description, String... tags) {
        return Counter.builder(name)
                .description(description)
                .tags(tags)
                .register(meterRegistry);
    }
    
    /**
     * 创建测量仪
     */
    public <T> Gauge gauge(String name, String description, T stateObject, 
                          java.util.function.ToDoubleFunction<T> valueFunction, String... tags) {
        return Gauge.builder(name, stateObject, valueFunction)
                .description(description)
                .tags(tags)
                .register(meterRegistry);
    }
    
    /**
     * 创建长任务计时器
     */
    public LongTaskTimer longTaskTimer(String name, String description, String... tags) {
        return LongTaskTimer.builder(name)
                .description(description)
                .tags(tags)
                .register(meterRegistry);
    }
    
    /**
     * 记录计时器样本 - 异步版本
     */
    public void recordTimer(String name, long duration, TimeUnit unit, String... tags) {
        try {
            Timer timer = timer(name, "", tags);
            timer.record(duration, unit);
        } catch (Exception e) {
            log.warn("记录计时器指标失败: {}", name, e);
        }
    }
    
    /**
     * 增加计数器 - 异步版本
     */
    public void incrementCounter(String name, String... tags) {
        try {
            Counter counter = counter(name, "", tags);
            counter.increment();
        } catch (Exception e) {
            log.warn("增加计数器指标失败: {}", name, e);
        }
    }
    
    /**
     * 记录自定义数值
     */
    public void recordValue(String name, double value, String... tags) {
        try {
            DistributionSummary.builder(name)
                    .tags(tags)
                    .register(meterRegistry)
                    .record(value);
        } catch (Exception e) {
            log.warn("记录数值指标失败: {}", name, e);
        }
    }
    
    /**
     * 安全执行指标记录
     */
    public void safeExecute(Runnable metricsAction) {
        try {
            metricsAction.run();
        } catch (Exception e) {
            log.warn("执行指标记录时发生异常", e);
        }
    }
    
    /**
     * 获取应用名称
     */
    public String getApplicationName() {
        return applicationName;
    }
} 